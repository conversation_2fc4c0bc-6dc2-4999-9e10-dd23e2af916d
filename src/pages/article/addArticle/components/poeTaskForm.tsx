import {
  poeGetPoeClientList,
} from '@/services/swagger/poe';
import {articleAddPoeArticle} from "@/services/swagger/article";
import {useModel} from '@@/exports';
import {
  ProFormCheckbox,
  ProFormDependency,
  ProFormInstance,
  ProForm
} from '@ant-design/pro-components';
import {Card, Flex, SelectProps, Cascader} from 'antd';
import React, {useEffect, useRef, useState} from 'react';
import {categoryGetAllArticleClassifications} from "@/services/swagger/category";
import {promoteGetAllPromotes} from "@/services/swagger/promote";

// Import common components
import ArticleFormBase from "@/pages/article/addArticle/components/common/ArticleFormBase";
import PromoteSelector from "@/pages/article/addArticle/components/common/PromoteSelector";
import ReferenceArticleSelector from "@/pages/article/addArticle/components/common/ReferenceArticleSelector";
import ImageGenerationSection from "@/pages/article/addArticle/components/common/ImageGenerationSection";
import TitleListCard from "@/pages/article/addArticle/components/common/TitleListCard";
import PoeClientSessionSelector from "@/pages/article/addArticle/components/common/PoeClientSessionSelector";

// 定义分类选项类型
interface ClassifyOption {
  label: string;
  value: string;
  children?: ClassifyOption[];
}

const PoeTaskForm: React.FC = () => {
  const [poe_clien_option, setPoeClientOption] = useState<SelectProps['options']>([]);
  const [classifyOptions, setClassifyOptions] = useState<ClassifyOption[]>([]);
  const [selectedClassify, setSelectedClassify] = useState<string[]>([]);
  const [selectedClassifyId, setSelectedClassifyId] = useState<string | undefined>(undefined);
  const [promoteMap, setPromoteMap] = useState<Map<string, string>>(new Map()); // 分类ID到Promote ID的映射

  const {initialState} = useModel('@@initialState');
  const poe_setting: API.PoeDefaultSetting | undefined = initialState?.poeSettings;

  const formRef = useRef<ProFormInstance<API.ArticleCreate>>();

  useEffect(() => {
    async function fetchData() {
      const res: API.ResponseBodyListPoeClientPublic_ = await poeGetPoeClientList({});
      if (res.succeed && res.data) {
        const option: SelectProps['options'] = [];
        res.data.forEach((item: any) => {
          option.push({
            value: item.id,
            label: item.name,
          });
        });
        setPoeClientOption(option);
      }

      // 获取分类数据
      fetchClassifyOptions();

      // 获取所有promote数据，建立分类到Promote的映射关系
      fetchPromotes();
    }

    fetchData();
  }, []);

  // 获取所有Promote并建立映射关系
  const fetchPromotes = async () => {
    try {
      const res = await promoteGetAllPromotes({});
      if (res.succeed && res.data?.items) {
        const newPromoteMap = new Map<string, string>();

        // 遍历所有promote，建立分类ID到promote ID的映射
        res.data.items.forEach((item: any) => {
          if (item.classify_id && item.id) {
            newPromoteMap.set(item.classify_id, item.id);
          }
        });

        setPromoteMap(newPromoteMap);
      }
    } catch (error) {
      console.error('获取Promote列表失败:', error);
    }
  };

  // 获取分类列表
  const fetchClassifyOptions = async () => {
    try {
      const res = await categoryGetAllArticleClassifications({ parent_only: false });
      if (res.succeed && res.data) {
        const parentMap = new Map();

        // 创建父级分类映射
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (!item.parent) {
            parentMap.set(item.id, {
              label: item.name,
              value: item.id || '',
              children: []
            });
          }
        });

        // 添加子分类到对应的父分类
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (item.parent && item.parent.id && parentMap.has(item.parent.id)) {
            parentMap.get(item.parent.id).children.push({
              label: item.name,
              value: item.id || ''
            });
          }
        });

        setClassifyOptions(Array.from(parentMap.values()));
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  };

  // 处理分类选择变化
  const handleClassifyChange = (value: string[]) => {
    setSelectedClassify(value);

    // 设置最终选择的分类ID（用于关联Promote）
    if (value && value.length > 0) {
      const classifyId = value[value.length - 1];
      setSelectedClassifyId(classifyId);

      // 检查是否有对应的Promote，如果有则自动选择
      if (promoteMap.has(classifyId)) {
        // 这里仅设置状态，PromoteSelector组件会通过自身的useEffect处理选择动作
      }
    } else {
      setSelectedClassifyId(undefined);
    }
  };

  // 当Promote变更时，可以在这里处理分类联动
  const handlePromoteChange = (promoteId: string) => {
    // 这里可以实现当选择Promote时自动选择对应的分类（可选）
    console.log('选择了Promote ID:', promoteId);
  };


  return (
    <ArticleFormBase
      formRef={formRef}
      onFinish={async (values) => {
        // 添加分类ID到提交数据
        if (selectedClassify && selectedClassify.length > 0) {
          values.category_id = selectedClassify[selectedClassify.length - 1];
        }
        return await articleAddPoeArticle(values);
      }}
    >
      <Card title={'文章生成选项'}>
        <Flex wrap={'wrap'} gap="large">
          <PoeClientSessionSelector
            clientOptions={poe_clien_option || []}
            clientIdFieldName={['article', 'client_id']}
            botFieldName={['article', 'bot']}
            nameSuffix={'article'}
            initialClientId={poe_setting?.article_client_id || undefined}
            initialBot={poe_setting?.article_bot || undefined}
            initialSessionId={poe_setting?.article_session_id || undefined}
            formRef={formRef}
          />
          <ReferenceArticleSelector
            platformFieldName="article_ref_platform"
            numFieldName="article_ref_num"
            initialPlatform={poe_setting?.article_ref_platform}
            initialNum={poe_setting?.article_ref_num}
          />
          <ProFormCheckbox
            name="article_generate_outline_first"
            label={'先生成提纲再生成文章'}
            initialValue={poe_setting?.article_generate_outline_first}
          />
          <ImageGenerationSection
            settingType="poe"
            initialValue={poe_setting?.generate_image}
          />

        </Flex>

        {/* 添加分类选择 */}
        <ProForm.Item
          label="文章分类"
          tooltip="选择文章所属的分类，便于管理"
          name="classify"
        >
          <Cascader
            options={classifyOptions}
            expandTrigger="hover"
            displayRender={(labels) => labels.join(' / ')}
            placeholder="请选择文章分类"
            onChange={(value) => handleClassifyChange(value as string[])}
            style={{ width: '100%' }}
            allowClear
          />
        </ProForm.Item>

        <PromoteSelector
          formRef={formRef}
          fieldName={['article', 'promote']}
          initialValue={poe_setting?.article_promote}
          classifyId={selectedClassifyId}
          onPromoteChange={handlePromoteChange}
        />
      </Card>
      <ProFormDependency
        key="article_generate_outline_first"
        name={['article_generate_outline_first']}
      >
        {({article_generate_outline_first}) => {
          if (!article_generate_outline_first) {
            return <></>;
          }
          return (
            <Card title={'生成提纲选项'}>
              <Flex wrap={'wrap'} gap="large">
                <PoeClientSessionSelector
                  clientOptions={poe_clien_option || []}
                  clientIdFieldName={['outline', 'client_id']}
                  botFieldName={['outline', 'bot']}
                  nameSuffix={'outline'}
                  initialClientId={poe_setting?.outline_client_id || undefined}
                  initialBot={poe_setting?.outline_bot || undefined}
                  initialSessionId={poe_setting?.outline_session_id || undefined}
                  formRef={formRef}
                />

              </Flex>
              <PromoteSelector
                formRef={formRef}
                fieldName={['outline', 'promote']}
                initialValue={poe_setting?.outline_promote}
                classifyId={selectedClassifyId}
                onPromoteChange={handlePromoteChange}
              />
            </Card>
          );
        }}
      </ProFormDependency>


      <TitleListCard form={formRef.current as any}/>
    </ArticleFormBase>
  );
};

export default PoeTaskForm;
