import React, {useEffect, useRef, useState} from "react";
import {Card, Flex, SelectProps, Cascader} from "antd";
import {
  thirdApiKeyGetModels,
  thirdApiKeyGetThirdApiKeyPlatform
} from "@/services/swagger/thirdApiKey";
// @ts-ignore
import {useModel} from "@@/exports";
import {
  ProFormInstance,
  ProFormSelect,
  ProForm
} from "@ant-design/pro-components";
import {articleAddImitateArticle} from "@/services/swagger/article";
import {categoryGetAllArticleClassifications} from "@/services/swagger/category";
import {promoteGetAllPromotes} from "@/services/swagger/promote";

// Import common components
import ArticleFormBase from "@/pages/article/addArticle/components/common/ArticleFormBase";
import PromoteSelector from "@/pages/article/addArticle/components/common/PromoteSelector";
import ReferenceArticleSelector from "@/pages/article/addArticle/components/common/ReferenceArticleSelector";
import ImageGenerationSection from "@/pages/article/addArticle/components/common/ImageGenerationSection";
import TitleContentUrlList from "@/pages/article/addArticle/components/titleContentUrlList";

// 定义分类选项类型
interface ClassifyOption {
  label: string;
  value: string;
  children?: ClassifyOption[];
}

const ImitateArticleForm: React.FC = () => {
  const formRef = useRef<ProFormInstance<API.ImitateArticleCreate> | null>(null);
  const [modelOption, setModelOption] = useState<SelectProps['options']>([]);
  const [platformOption, setPlatformOption] = useState<SelectProps['options']>([]);
  const [classifyOptions, setClassifyOptions] = useState<ClassifyOption[]>([]);
  const [selectedClassify, setSelectedClassify] = useState<string[]>([]);
  const [selectedClassifyId, setSelectedClassifyId] = useState<string | undefined>(undefined);
  const [promoteMap, setPromoteMap] = useState<Map<string, string>>(new Map()); // 分类ID到Promote ID的映射

  const {initialState} = useModel('@@initialState');
  const api_setting: API.ApiDefaultSetting | undefined = initialState?.apiSettings;

  // Force re-render when formRef is available
  const [formReady, setFormReady] = useState(false);
  useEffect(() => {
    if (formRef.current && !formReady) {
      setFormReady(true);
      console.log('ImitateArticleForm form is ready:', formRef.current);
    }
  }, [formRef.current, formReady]);

  useEffect(() => {
    async function fetchData() {
      const res: API.ResponseBodyListThirdApiKeyPlatformOption_ = await thirdApiKeyGetThirdApiKeyPlatform();
      if (res.succeed && res.data) {
        setPlatformOption(res.data);
      }

      // 获取分类数据
      fetchClassifyOptions();

      // 获取所有promote数据，建立分类到Promote的映射关系
      fetchPromotes();
    }

    fetchData();
  }, []);

  // 获取所有Promote并建立映射关系
  const fetchPromotes = async () => {
    try {
      const res = await promoteGetAllPromotes({});
      if (res.succeed && res.data?.items) {
        const newPromoteMap = new Map<string, string>();

        // 遍历所有promote，建立分类ID到promote ID的映射
        res.data.items.forEach((item: any) => {
          if (item.classify_id && item.id) {
            newPromoteMap.set(item.classify_id, item.id);
          }
        });

        setPromoteMap(newPromoteMap);
      }
    } catch (error) {
      console.error('获取Promote列表失败:', error);
    }
  };

  // 获取分类列表
  const fetchClassifyOptions = async () => {
    try {
      const res = await categoryGetAllArticleClassifications({ parent_only: false });
      if (res.succeed && res.data) {
        const parentMap = new Map();

        // 创建父级分类映射
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (!item.parent) {
            parentMap.set(item.id, {
              label: item.name,
              value: item.id || '',
              children: []
            });
          }
        });

        // 添加子分类到对应的父分类
        res.data.forEach((item: API.ArticleCategoryPublic) => {
          if (item.parent && item.parent.id && parentMap.has(item.parent.id)) {
            parentMap.get(item.parent.id).children.push({
              label: item.name,
              value: item.id || ''
            });
          }
        });

        setClassifyOptions(Array.from(parentMap.values()));
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    }
  };

  // 处理分类选择变化
  const handleClassifyChange = (value: string[]) => {
    setSelectedClassify(value);

    // 设置最终选择的分类ID（用于关联Promote）
    if (value && value.length > 0) {
      const classifyId = value[value.length - 1];
      setSelectedClassifyId(classifyId);

      // 检查是否有对应的Promote，如果有则自动选择
      if (promoteMap.has(classifyId)) {
        // 这里仅设置状态，PromoteSelector组件会通过自身的useEffect处理选择动作
      }
    } else {
      setSelectedClassifyId(undefined);
    }
  };

  // 当Promote变更时，可以在这里处理分类联动
  const handlePromoteChange = (promoteId: string) => {
    // 这里可以实现当选择Promote时自动选择对应的分类（可选）
    console.log('选择了Promote ID:', promoteId);
  };

  const onPlatformChanged = async (value: any) => {
    const res: API.ResponseBodyListStr_ = await thirdApiKeyGetModels({platform: value})

    if (res.succeed && res.data) {
      setModelOption(
        res.data.map((item: string) => ({
          label: item,
          value: item,
        }))
      )
      for (let optionKey of res.data) {
        if (optionKey === api_setting?.model) {
          formRef.current?.setFieldsValue({model: optionKey});
          return;
        }
      }
      formRef.current?.setFieldsValue({model: undefined})
    }
  }

  return (
    <ArticleFormBase
      formRef={formRef}
      onFinish={async (values) => {
        // 添加分类ID到提交数据
        if (selectedClassify && selectedClassify.length > 0) {
          values.category_id = selectedClassify[selectedClassify.length - 1];
        }
        return await articleAddImitateArticle(values);
      }}
    >
      <Card title="洗搞助手设置">
        <Flex wrap={'wrap'} gap="large">
          <ProFormSelect
            width={'sm'}
            label="文章生成默认平台"
            name="platform"
            initialValue={api_setting?.platform}
            options={platformOption}
            required={true}
            onChange={onPlatformChanged}
          ></ProFormSelect>
          <ProFormSelect
            showSearch={true}
            shouldUpdate={true}
            options={modelOption}
            width={'sm'}
            label="默认模型选择"
            name="model"
            initialValue={api_setting?.model}
          ></ProFormSelect>
          <ReferenceArticleSelector
            platformFieldName="ref_platform"
            numFieldName="ref_num"
            initialPlatform={api_setting?.ref_platform}
            initialNum={api_setting?.ref_num}
          />
          <ImageGenerationSection
            settingType="api"
            initialValue={api_setting?.generate_image}
          />
        </Flex>

        {/* 添加分类选择 */}
        <ProForm.Item
          label="文章分类"
          tooltip="选择文章所属的分类，便于管理"
          name="classify"
        >
          <Cascader
            options={classifyOptions}
            expandTrigger="hover"
            displayRender={(labels) => labels.join(' / ')}
            placeholder="请选择文章分类"
            onChange={(value) => handleClassifyChange(value as string[])}
            style={{ width: '100%' }}
            allowClear
          />
        </ProForm.Item>

        <PromoteSelector
          formRef={formRef}
          fieldName="promote"
          initialValue={api_setting?.promote}
          classifyId={selectedClassifyId}
          onPromoteChange={handlePromoteChange}
        />
      </Card>

      <Card title="标题内容URL列表">
        {formReady && formRef.current && (
          <TitleContentUrlList form={formRef.current} />
        )}
      </Card>
    </ArticleFormBase>
  )
}

export default ImitateArticleForm;
